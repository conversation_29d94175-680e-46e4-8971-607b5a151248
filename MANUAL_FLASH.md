# 手动烧录指南

## 🔧 解决"打开文件失败"问题

### 立即解决方案

#### 方案1：使用绝对路径
```cmd
# 获取当前目录的完整路径
cd
# 复制显示的路径，然后使用完整路径烧录

# 示例 (替换为您的实际路径)
st-flash --format ihex write "C:\Users\<USER>\Desktop\project\build\servo_driver.hex"
```

#### 方案2：复制文件到简单路径
```cmd
# 创建临时目录
mkdir C:\temp

# 复制hex文件
copy build\servo_driver.hex C:\temp\test.hex

# 烧录
st-flash --format ihex write C:\temp\test.hex
```

#### 方案3：使用二进制格式
```cmd
# 如果hex文件有问题，尝试bin格式
st-flash erase
st-flash write build\servo_driver.bin 0x08000000
```

#### 方案4：使用测试文件
```cmd
# 使用提供的测试文件
st-flash --format ihex write build\servo_test.hex
```

### 逐步手动烧录

#### 步骤1：检查设备
```cmd
# 检查ST-Link连接
st-info --probe

# 应该显示类似输出:
# Found 1 stlink programmers
# serial: 066DFF485750717867114355
# openocd: "\interface\stlink-v2.cfg"
# flash: 65536 (pagesize: 1024)
# sram: 20480
# chipid: 0x0410
# descr: F1 Medium-density device
```

#### 步骤2：验证文件
```cmd
# 检查文件是否存在
dir build\servo_driver.hex

# 检查文件大小 (应该大于0字节)
# 检查文件内容开头
type build\servo_driver.hex | more
# 应该以 :020000040800F2 开头
```

#### 步骤3：尝试不同烧录方法
```cmd
# 方法A: 标准hex烧录
st-flash --format ihex write build\servo_driver.hex

# 方法B: 指定起始地址的hex烧录
st-flash --format ihex write build\servo_driver.hex 0x08000000

# 方法C: 二进制烧录
st-flash erase
st-flash write build\servo_driver.bin 0x08000000

# 方法D: 降低连接速度
st-flash --freq=480 write build\servo_driver.bin 0x08000000
```

#### 步骤4：验证烧录
```cmd
# 读取Flash内容验证
st-flash read verify.bin 0x08000000 0x1000

# 比较文件 (可选)
fc /b build\servo_driver.bin verify.bin
```

## 🛠️ 使用其他工具

### STM32CubeProgrammer (推荐)
1. 下载并安装 STM32CubeProgrammer
2. 启动程序
3. 选择 ST-LINK 连接
4. 点击 "Connect"
5. 在 "Erasing & Programming" 选项卡中:
   - 选择hex文件
   - 点击 "Start Programming"

### J-Link Commander
```cmd
# 启动J-Link
JLinkExe

# 在J-Link命令行中输入:
device STM32F103C8
si SWD
speed 4000
connect
loadfile build\servo_driver.hex
r
g
exit
```

### OpenOCD + Telnet
```cmd
# 终端1: 启动OpenOCD
openocd -f config\openocd.cfg

# 终端2: 连接并烧录
telnet localhost 4444

# 在telnet中执行:
reset halt
flash write_image erase build\servo_driver.hex
reset run
exit
```

## 📋 常见错误及解决

### 错误1: "No such file or directory"
```cmd
# 解决方案: 使用绝对路径或检查当前目录
pwd
ls build/
st-flash --format ihex write "$(pwd)/build/servo_driver.hex"
```

### 错误2: "Permission denied"
```cmd
# 解决方案: 以管理员身份运行
# 右键命令提示符 -> "以管理员身份运行"
```

### 错误3: "Invalid hex file"
```cmd
# 解决方案: 检查hex文件格式
# 使用文本编辑器打开hex文件
# 确保以 :020000040800F2 开头
# 以 :00000001FF 结尾
```

### 错误4: "Target not found"
```cmd
# 解决方案: 检查硬件连接
# SWDIO -> PA13
# SWCLK -> PA14  
# GND -> GND
# 3.3V -> 3.3V

# 尝试按住复位键后连接
st-flash --connect-under-reset write build\servo_driver.bin 0x08000000
```

## 🔍 调试技巧

### 收集详细日志
```cmd
# 启用详细输出
st-flash --debug write build\servo_driver.bin 0x08000000 > flash_log.txt 2>&1

# 查看日志
notepad flash_log.txt
```

### 检查设备状态
```cmd
# 获取设备详细信息
st-info --probe --descr

# 检查Flash状态
st-flash --freq=480 read test.bin 0x08000000 1024
```

### 测试基本连接
```cmd
# 简单的连接测试
st-info --probe --connect-under-reset

# 如果成功，说明硬件连接正常
```

## 📞 获取帮助

### 自动诊断
```cmd
# 运行自动诊断脚本
scripts\test_flash.bat
```

### 手动检查清单
- [ ] ST-Link USB连接正常
- [ ] SWD连接线正确 (PA13/PA14)
- [ ] 开发板电源正常 (3.3V)
- [ ] hex文件存在且格式正确
- [ ] 烧录工具已安装
- [ ] 以管理员身份运行

### 联系支持
如果问题仍然存在，请提供：
1. 完整的错误信息
2. 使用的命令
3. 硬件连接照片
4. 操作系统版本
5. ST-Link工具版本

---

**快速测试**: 运行 `scripts\test_flash.bat` 进行全面诊断
