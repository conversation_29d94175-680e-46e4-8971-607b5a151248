# C068开发板舵机驱动程序

本项目为C068开发板提供舵机控制驱动程序，支持PWM信号生成和精确角度控制。

## 项目特性

- 🎯 精确角度控制 (0-180度)
- ⚡ 高性能PWM信号生成 (50Hz)
- 🔧 多种烧录方式支持 (ST-Link/J-Link/OpenOCD)
- 📊 实时状态监控和调试输出
- 🛡️ 完善的错误处理机制
- 📱 LED状态指示
- 🔄 自动化编译和烧录脚本

## 项目结构

```
├── src/                    # 源代码目录
│   ├── main.c             # 主程序文件
│   ├── servo_driver.c     # 舵机驱动实现
│   └── system_init.c      # 系统初始化
├── inc/                    # 头文件目录
│   ├── servo_driver.h     # 舵机驱动头文件
│   ├── system_config.h    # 系统配置
│   └── board_config.h     # 开发板配置
├── build/                  # 编译输出目录
├── scripts/               # 脚本目录
│   ├── flash.sh          # 烧录脚本
│   ├── build.sh          # 编译脚本
│   └── jlink_flash.jlink # J-Link烧录配置
├── config/                # 配置文件目录
│   ├── linker.ld         # 链接脚本
│   └── openocd.cfg       # OpenOCD配置
├── Makefile              # 编译配置
└── README.md             # 说明文档
```

## 硬件要求

### 开发板
- **C068开发板** (基于STM32F103C8芯片)
- **系统时钟**: 72MHz
- **Flash**: 64KB
- **RAM**: 20KB

### 舵机
- **推荐型号**: SG90、MG90S或类似9g舵机
- **工作电压**: 4.8V-6V
- **控制信号**: PWM (50Hz, 0.5ms-2.5ms脉宽)

### 其他硬件
- 连接线 (杜邦线)
- 5V电源适配器
- USB转串口模块 (调试用，可选)

## 软件要求

### 必需工具
- **ARM GCC工具链** (arm-none-eabi-gcc)
- **Make工具**
- **烧录工具** (选择其一):
  - ST-Link Utility + st-flash
  - J-Link + JLinkExe
  - OpenOCD

### 可选工具
- **串口调试工具** (PuTTY, minicom等)
- **Git** (版本控制)

## 快速开始

### 1. 环境准备

#### Ubuntu/Debian系统
```bash
# 安装ARM工具链
sudo apt-get update
sudo apt-get install gcc-arm-none-eabi make

# 安装烧录工具 (选择其一)
sudo apt-get install openocd        # OpenOCD
sudo apt-get install stlink-tools   # ST-Link
```

#### Windows系统
1. 下载并安装 [GNU Arm Embedded Toolchain](https://developer.arm.com/tools-and-software/open-source-software/developer-tools/gnu-toolchain/gnu-rm)
2. 下载并安装 [Make for Windows](http://gnuwin32.sourceforge.net/packages/make.htm)
3. 安装烧录工具:
   - [ST-Link Utility](https://www.st.com/en/development-tools/stsw-link004.html)
   - [J-Link Software](https://www.segger.com/downloads/jlink/)
   - [OpenOCD](https://openocd.org/pages/getting-openocd.html)

### 2. 硬件连接

#### 舵机连接
```
C068开发板    舵机
PA6    <-->  信号线 (黄/白)
5V     <-->  电源线 (红)
GND    <-->  地线   (黑/棕)
```

#### 调试串口连接 (可选)
```
C068开发板    USB转串口
PA9    <-->  RX
PA10   <-->  TX
GND    <-->  GND
```

#### LED指示灯
- **PC13**: 板载LED，用于状态指示

### 3. 编译项目

#### 方法一：使用Makefile
```bash
# 清理并编译
make clean
make all

# 查看编译信息
make info

# 生成反汇编文件
make disasm
```

#### 方法二：使用编译脚本
```bash
# 普通编译
./scripts/build.sh

# 清理后编译
./scripts/build.sh --clean

# 详细输出编译
./scripts/build.sh --verbose
```

### 4. 烧录程序

#### 方法一：使用Makefile
```bash
# 自动检测烧录器
make flash

# 指定烧录器
make flash-stlink    # ST-Link
make flash-jlink     # J-Link
```

#### 方法二：使用烧录脚本
```bash
# 自动检测烧录器
./scripts/flash.sh

# 强制使用特定烧录器
./scripts/flash.sh --stlink   # ST-Link
./scripts/flash.sh --jlink    # J-Link
./scripts/flash.sh --openocd  # OpenOCD

# 查看连接说明
./scripts/flash.sh --connect
```

### 5. 运行和调试

#### 观察运行状态
1. **LED指示**: PC13 LED会根据舵机运动状态闪烁
2. **舵机动作**: 舵机会执行预设的测试序列
3. **串口输出**: 连接串口工具查看调试信息 (115200波特率)

#### 调试模式
```bash
# 启动OpenOCD服务器
make openocd

# 在另一个终端启动GDB调试
make debug
```

## 详细配置说明

### PWM配置参数
- **频率**: 50Hz (20ms周期)
- **脉宽范围**: 0.5ms - 2.5ms
- **角度范围**: 0° - 180°
- **定时器**: TIM3 Channel 1
- **输出引脚**: PA6

### 系统配置
- **系统时钟**: 72MHz (HSE + PLL)
- **APB1时钟**: 36MHz
- **APB2时钟**: 72MHz
- **调试串口**: USART1 (115200, 8N1)

## 常见问题解决

### 编译问题
1. **工具链未找到**
   ```
   解决方案: 确保arm-none-eabi-gcc在PATH环境变量中
   ```

2. **标准库缺失**
   ```
   解决方案: 下载STM32F10x标准外设库和CMSIS库
   ```

### 烧录问题
1. **设备未检测到**
   ```
   解决方案: 检查USB连接和驱动程序安装
   ```

2. **烧录失败**
   ```
   解决方案: 检查开发板电源和复位状态
   ```

### 运行问题
1. **舵机不动作**
   ```
   检查项目:
   - 电源电压是否为5V
   - 信号线连接是否正确
   - 舵机是否损坏
   ```

2. **角度不准确**
   ```
   解决方案: 调整board_config.h中的脉宽参数
   ```

## API参考

### 主要函数

#### 初始化函数
```c
servo_error_t Servo_Init(void);
```

#### 角度控制
```c
servo_error_t Servo_SetAngle(uint16_t angle);    // 设置角度 (0-180)
uint16_t Servo_GetAngle(void);                   // 获取当前角度
servo_error_t Servo_Center(void);                // 归中 (90度)
```

#### 脉宽控制
```c
servo_error_t Servo_SetPulseWidth(uint16_t pulse_width);  // 设置脉宽 (微秒)
uint16_t Servo_GetPulseWidth(void);                      // 获取当前脉宽
```

#### 测试函数
```c
void Servo_Sweep(uint16_t start_angle, uint16_t end_angle,
                 uint16_t step, uint32_t delay_ms);      // 扫描测试
```

#### 状态查询
```c
servo_status_t* Servo_GetStatus(void);                   // 获取舵机状态
```

### 错误代码
```c
typedef enum {
    SERVO_OK = 0,                    // 成功
    SERVO_ERROR_INVALID_ANGLE,       // 无效角度
    SERVO_ERROR_INIT_FAILED,         // 初始化失败
    SERVO_ERROR_TIMEOUT              // 超时
} servo_error_t;
```

## 自定义配置

### 修改PWM参数
编辑 `inc/board_config.h` 文件:
```c
#define SERVO_PULSE_MIN         500   // 最小脉宽 (微秒)
#define SERVO_PULSE_MAX         2500  // 最大脉宽 (微秒)
#define SERVO_PULSE_CENTER      1500  // 中心脉宽 (微秒)
```

### 修改引脚配置
```c
#define SERVO_PWM_PIN           GPIO_Pin_6   // PWM输出引脚
#define SERVO_PWM_PORT          GPIOA        // GPIO端口
```

### 启用/禁用功能
```c
#define ENABLE_DEBUG            1    // 调试输出
#define ENABLE_LED_INDICATOR    1    // LED指示
```

## 许可证

本项目采用 MIT 许可证，详见 LICENSE 文件。

## 贡献

欢迎提交 Issue 和 Pull Request 来改进本项目。

## 联系方式

如有问题或建议，请通过以下方式联系：
- 提交 GitHub Issue
- 发送邮件至项目维护者

---

**注意**: 请确保在操作前仔细阅读硬件连接说明，错误的连接可能损坏设备。
