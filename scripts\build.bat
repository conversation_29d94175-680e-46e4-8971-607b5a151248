@echo off
REM C068开发板舵机驱动程序编译脚本 (Windows版本)

setlocal enabledelayedexpansion

REM 项目配置
set PROJECT_NAME=servo_driver
set BUILD_DIR=build
set TOOLCHAIN_PREFIX=arm-none-eabi-

REM 颜色定义 (Windows CMD)
set RED=[91m
set GREEN=[92m
set YELLOW=[93m
set BLUE=[94m
set NC=[0m

REM 函数：打印带颜色的消息
:print_message
echo %~1%~2%NC%
goto :eof

REM 函数：检查工具链
:check_toolchain
call :print_message "%BLUE%" "检查ARM工具链..."

where %TOOLCHAIN_PREFIX%gcc >nul 2>&1
if errorlevel 1 (
    call :print_message "%RED%" "错误: ARM GCC工具链未找到!"
    call :print_message "%YELLOW%" "请安装 GNU Arm Embedded Toolchain"
    call :print_message "%YELLOW%" "下载地址: https://developer.arm.com/tools-and-software/open-source-software/developer-tools/gnu-toolchain/gnu-rm"
    exit /b 1
)

call :print_message "%GREEN%" "工具链检查通过"
%TOOLCHAIN_PREFIX%gcc --version | findstr /C:"gcc"
goto :eof

REM 函数：检查Make工具
:check_make
where make >nul 2>&1
if errorlevel 1 (
    call :print_message "%RED%" "错误: Make工具未找到!"
    call :print_message "%YELLOW%" "请安装 Make for Windows"
    call :print_message "%YELLOW%" "下载地址: http://gnuwin32.sourceforge.net/packages/make.htm"
    exit /b 1
)
goto :eof

REM 函数：清理构建目录
:clean_build
call :print_message "%BLUE%" "清理构建目录..."
if exist "%BUILD_DIR%" (
    rmdir /s /q "%BUILD_DIR%"
    call :print_message "%GREEN%" "构建目录已清理"
)
goto :eof

REM 函数：创建构建目录
:create_build_dir
call :print_message "%BLUE%" "创建构建目录..."
if not exist "%BUILD_DIR%" (
    mkdir "%BUILD_DIR%"
)
call :print_message "%GREEN%" "构建目录已创建"
goto :eof

REM 函数：编译项目
:build_project
call :print_message "%BLUE%" "开始编译项目..."

make all
if errorlevel 1 (
    call :print_message "%RED%" "编译失败!"
    exit /b 1
)

call :print_message "%GREEN%" "编译成功!"

REM 显示文件大小信息
if exist "%BUILD_DIR%\%PROJECT_NAME%.elf" (
    call :print_message "%BLUE%" "文件大小信息:"
    %TOOLCHAIN_PREFIX%size "%BUILD_DIR%\%PROJECT_NAME%.elf"
    
    call :print_message "%BLUE%" "生成的文件:"
    dir "%BUILD_DIR%\%PROJECT_NAME%.*" /b
)
goto :eof

REM 函数：显示帮助信息
:show_help
echo 用法: %0 [选项]
echo.
echo 选项:
echo   -c, --clean     清理后编译
echo   -h, --help      显示帮助信息
echo.
echo 示例:
echo   %0              # 普通编译
echo   %0 -c           # 清理后编译
goto :eof

REM 主函数
:main
set clean_first=false

REM 解析命令行参数
:parse_args
if "%~1"=="" goto :start_build
if "%~1"=="-c" set clean_first=true
if "%~1"=="--clean" set clean_first=true
if "%~1"=="-h" goto :show_help_and_exit
if "%~1"=="--help" goto :show_help_and_exit
shift
goto :parse_args

:show_help_and_exit
call :show_help
exit /b 0

:start_build
call :print_message "%GREEN%" "=== C068舵机驱动程序编译脚本 ==="

REM 检查工具链
call :check_toolchain
if errorlevel 1 exit /b 1

REM 检查Make工具
call :check_make
if errorlevel 1 exit /b 1

REM 清理构建目录 (如果需要)
if "%clean_first%"=="true" (
    call :clean_build
)

REM 创建构建目录
call :create_build_dir

REM 编译项目
call :build_project
if errorlevel 1 exit /b 1

call :print_message "%GREEN%" "=== 编译完成 ==="
call :print_message "%BLUE%" "下一步: 使用 'scripts\flash.bat' 烧录程序"

goto :eof

REM 执行主函数
call :main %*
