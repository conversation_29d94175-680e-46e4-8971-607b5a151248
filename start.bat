@echo off
REM C068开发板舵机驱动程序 - 快速启动脚本

echo ========================================
echo    C068开发板舵机驱动程序
echo ========================================
echo.

:menu
echo 请选择操作:
echo 1. 编译项目
echo 2. 烧录程序
echo 3. 清理并重新编译
echo 4. 显示硬件连接说明
echo 5. 查看项目信息
echo 6. 退出
echo.
set /p choice=请输入选择 (1-6): 

if "%choice%"=="1" goto compile
if "%choice%"=="2" goto flash
if "%choice%"=="3" goto clean_compile
if "%choice%"=="4" goto show_connections
if "%choice%"=="5" goto show_info
if "%choice%"=="6" goto exit
echo 无效选择，请重新输入
echo.
goto menu

:compile
echo.
echo [编译项目]
scripts\build.bat
pause
goto menu

:flash
echo.
echo [烧录程序]
scripts\flash.bat
pause
goto menu

:clean_compile
echo.
echo [清理并重新编译]
scripts\build.bat --clean
pause
goto menu

:show_connections
echo.
echo [硬件连接说明]
scripts\flash.bat --connect
pause
goto menu

:show_info
echo.
echo [项目信息]
echo 项目名称: C068开发板舵机驱动程序
echo 目标芯片: STM32F103C8 (C068开发板)
echo 功能特性: PWM舵机控制 (0-180度)
echo 编译工具: ARM GCC + Make
echo 烧录支持: ST-Link / J-Link / OpenOCD
echo.
echo 项目文件结构:
echo   src/           - 源代码
echo   inc/           - 头文件
echo   scripts/       - 编译烧录脚本
echo   config/        - 配置文件
echo   build/         - 编译输出
echo.
echo 主要功能:
echo   - 精确角度控制 (0-180度)
echo   - 50Hz PWM信号生成
echo   - 实时状态监控
echo   - LED状态指示
echo   - 串口调试输出
echo.
pause
goto menu

:exit
echo 感谢使用！
exit /b 0
