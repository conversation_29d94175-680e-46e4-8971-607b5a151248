@echo off
REM C068开发板舵机驱动程序 - 快速启动脚本

echo ========================================
echo    C068开发板舵机驱动程序
echo ========================================
echo.

:menu
echo 请选择操作:
echo 1. 编译项目
echo 2. 烧录程序
echo 3. 清理并重新编译
echo 4. 显示硬件连接说明
echo 5. 查看项目信息
echo 6. 烧录测试和诊断
echo 7. 故障排除指南
echo 8. 退出
echo.
set /p choice=请输入选择 (1-8):

if "%choice%"=="1" goto compile
if "%choice%"=="2" goto flash
if "%choice%"=="3" goto clean_compile
if "%choice%"=="4" goto show_connections
if "%choice%"=="5" goto show_info
if "%choice%"=="6" goto test_flash
if "%choice%"=="7" goto troubleshooting
if "%choice%"=="8" goto exit
echo 无效选择，请重新输入
echo.
goto menu

:compile
echo.
echo [编译项目]
scripts\build.bat
pause
goto menu

:flash
echo.
echo [烧录程序]
scripts\flash.bat
pause
goto menu

:clean_compile
echo.
echo [清理并重新编译]
scripts\build.bat --clean
pause
goto menu

:show_connections
echo.
echo [硬件连接说明]
scripts\flash.bat --connect
pause
goto menu

:show_info
echo.
echo [项目信息]
echo 项目名称: C068开发板舵机驱动程序
echo 目标芯片: STM32F103C8 (C068开发板)
echo 功能特性: PWM舵机控制 (0-180度)
echo 编译工具: ARM GCC + Make
echo 烧录支持: ST-Link / J-Link / OpenOCD
echo.
echo 项目文件结构:
echo   src/           - 源代码
echo   inc/           - 头文件
echo   scripts/       - 编译烧录脚本
echo   config/        - 配置文件
echo   build/         - 编译输出
echo.
echo 主要功能:
echo   - 精确角度控制 (0-180度)
echo   - 50Hz PWM信号生成
echo   - 实时状态监控
echo   - LED状态指示
echo   - 串口调试输出
echo.
pause
goto menu

:test_flash
echo.
echo [烧录测试和诊断]
scripts\test_flash.bat
pause
goto menu

:troubleshooting
echo.
echo [故障排除指南]
echo 正在打开故障排除文档...
if exist FLASH_TROUBLESHOOTING.md (
    notepad FLASH_TROUBLESHOOTING.md
) else (
    echo 故障排除文档未找到
    echo.
    echo 常见问题解决方案:
    echo 1. 检查文件路径是否正确
    echo 2. 确认烧录器连接正常
    echo 3. 验证开发板电源状态
    echo 4. 尝试以管理员身份运行
    echo 5. 使用绝对路径烧录
)
pause
goto menu

:exit
echo 感谢使用！
exit /b 0
