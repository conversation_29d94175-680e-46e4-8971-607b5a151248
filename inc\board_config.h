#ifndef BOARD_CONFIG_H
#define BOARD_CONFIG_H

/* C068开发板配置文件 */

/* 系统时钟配置 */
#define SYSTEM_CLOCK_FREQ       72000000UL  /* 72MHz系统时钟 */
#define APB1_CLOCK_FREQ         36000000UL  /* APB1时钟 */
#define APB2_CLOCK_FREQ         72000000UL  /* APB2时钟 */

/* 舵机PWM配置 */
#define SERVO_PWM_TIMER         TIM3        /* 使用定时器3 */
#define SERVO_PWM_CHANNEL       1           /* 使用通道1 */
#define SERVO_PWM_PIN           GPIO_Pin_6  /* PA6引脚 */
#define SERVO_PWM_PORT          GPIOA       /* PORTA */
#define SERVO_PWM_RCC           RCC_APB2Periph_GPIOA

/* PWM参数配置 */
#define SERVO_PWM_FREQUENCY     50          /* 50Hz PWM频率 */
#define SERVO_PWM_PERIOD        20000       /* 20ms周期 (微秒) */
#define SERVO_PULSE_MIN         500         /* 最小脉宽 0.5ms (微秒) */
#define SERVO_PULSE_MAX         2500        /* 最大脉宽 2.5ms (微秒) */
#define SERVO_PULSE_CENTER      1500        /* 中心脉宽 1.5ms (微秒) */

/* 舵机角度范围 */
#define SERVO_ANGLE_MIN         0           /* 最小角度 */
#define SERVO_ANGLE_MAX         180         /* 最大角度 */
#define SERVO_ANGLE_CENTER      90          /* 中心角度 */

/* LED指示灯配置 */
#define LED_PIN                 GPIO_Pin_13 /* PC13引脚 */
#define LED_PORT                GPIOC       /* PORTC */
#define LED_RCC                 RCC_APB2Periph_GPIOC

/* 调试串口配置 */
#define DEBUG_USART             USART1      /* 使用USART1 */
#define DEBUG_USART_BAUDRATE    115200      /* 波特率 */
#define DEBUG_TX_PIN            GPIO_Pin_9  /* PA9 */
#define DEBUG_RX_PIN            GPIO_Pin_10 /* PA10 */
#define DEBUG_GPIO_PORT         GPIOA
#define DEBUG_GPIO_RCC          RCC_APB2Periph_GPIOA
#define DEBUG_USART_RCC         RCC_APB2Periph_USART1

#endif /* BOARD_CONFIG_H */
